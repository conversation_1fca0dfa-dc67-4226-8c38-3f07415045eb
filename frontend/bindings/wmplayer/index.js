// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

import * as AlbumService from "./albumservice.js";
import * as CacheService from "./cacheservice.js";
import * as DiscoverService from "./discoverservice.js";
import * as DownloadService from "./downloadservice.js";
import * as FavoritesService from "./favoritesservice.js";
import * as HomepageService from "./homepageservice.js";
import * as LocalMusicService from "./localmusicservice.js";
import * as LoginService from "./loginservice.js";
import * as MediaKeyService from "./mediakeyservice.js";
import * as PlayHistoryService from "./playhistoryservice.js";
import * as PlaylistService from "./playlistservice.js";
import * as SearchService from "./searchservice.js";
import * as SettingsService from "./settingsservice.js";
export {
    AlbumService,
    CacheService,
    DiscoverService,
    DownloadService,
    FavoritesService,
    HomepageService,
    LocalMusicService,
    LoginService,
    MediaKeyService,
    PlayHistoryService,
    PlaylistService,
    SearchService,
    SettingsService
};

export {
    AIRecommendData,
    AIRecommendResponse,
    AddDownloadRecordRequest,
    AddFavoriteRequest,
    AddFavoriteResponse,
    AddPlayHistoryRequest,
    AddToPlaylistRequest,
    AlbumDetailData,
    AlbumDetailResponse,
    AlbumSongData,
    AlbumSongsResponse,
    ApiResponse,
    AudioFileResponse,
    BehaviorSettings,
    CacheResponse,
    CaptchaData,
    CaptchaResponse,
    DailyRecommendData,
    DailyRecommendResponse,
    DeleteDownloadRecordRequest,
    DownloadRecord,
    DownloadRecordsData,
    DownloadRecordsResponse,
    DownloadSettings,
    FavoritesSongData,
    FavoritesSongResponse,
    FmRequestParams,
    FmResponse,
    FmSongData,
    FolderMusicGroup,
    FolderSelectResponse,
    GetDownloadRecordsRequest,
    GetPlayHistoryRequest,
    HotSearchCategory,
    HotSearchData,
    HotSearchKeyword,
    HotSearchResponse,
    HotkeysSettings,
    InterfaceSettings,
    LocalMusicFile,
    LocalMusicResponse,
    LocalMusicStats,
    LoginData,
    LoginResponse,
    NewAlbumCategoryResponse,
    NewAlbumData,
    NewAlbumResponse,
    NewSongData,
    NewSongResponse,
    OSDLyricsResponse,
    PlayHistoryData,
    PlayHistoryRecord,
    PlayHistoryResponse,
    PlaybackSettings,
    PlayerPlaylistData,
    PlayerPlaylistResponse,
    PlayerPlaylistSong,
    PlaylistData,
    PlaylistResponse,
    PrivacySettings,
    QRCodeData,
    QRCodeResponse,
    QRKeyData,
    QRKeyResponse,
    QRStatusData,
    QRStatusResponse,
    QualitySettings,
    RecommendSongData,
    RecommendSongResponse,
    ResAlbum,
    ResArtist,
    ResMV,
    ResPlaylist,
    ResSong,
    SearchAlbumData,
    SearchArtistData,
    SearchMVData,
    SearchPlaylistData,
    SearchResponse,
    SearchResults,
    SearchSongData,
    SearchSuggestData,
    SearchSuggestResponse,
    SetPlaylistRequest,
    Settings,
    SongUrlData,
    SongUrlResponse,
    UpdatePlayModeRequest,
    UserDetailData,
    UserDetailResponse,
    VipDetailData,
    VipDetailResponse
} from "./models.js";
