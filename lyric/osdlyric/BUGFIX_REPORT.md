# OSD歌词程序退出问题修复报告

## 问题描述
用户报告 `lyric/osdlyric` 下的歌词程序在播放几首歌曲后会异常退出。

## 问题分析

通过代码审查，发现了以下几个可能导致程序退出的问题：

### 1. 内存管理问题
**位置**: `sse_write_callback` 函数
**问题**: 
- 缺少输入参数有效性检查
- `g_realloc` 失败时没有适当的错误处理
- 没有检查OSD对象的有效性

**影响**: 可能导致内存访问错误和程序崩溃

### 2. 线程安全问题
**位置**: `sse_connection_thread` 函数
**问题**:
- SSE连接线程和主线程之间存在竞争条件
- 程序退出时线程可能仍在访问已释放的资源
- 缺少适当的线程同步机制

**影响**: 可能导致段错误和程序异常退出

### 3. 定时器清理问题
**位置**: KRC渐进式播放相关代码
**问题**:
- 定时器回调函数缺少状态有效性检查
- 定时器清理不够彻底
- 可能存在定时器回调访问已释放内存的情况

**影响**: 可能导致程序在歌曲切换时崩溃

### 4. 资源清理不完整
**位置**: `osd_lyrics_cleanup` 函数
**问题**:
- 清理顺序不当
- 缺少详细的清理日志
- 没有标记对象为无效状态

**影响**: 可能导致资源泄漏和程序不稳定

## 修复方案

### 1. 改进SSE回调函数错误处理

```c
// 修复前
sse_data->buffer = g_realloc(sse_data->buffer, sse_data->buffer_size + realsize + 1);
if (sse_data->buffer == NULL) {
    return 0;
}

// 修复后
// 检查输入参数有效性
if (!sse_data || !contents || realsize == 0) {
    return 0;
}

// 检查OSD对象是否仍然有效
if (!sse_data->osd || !sse_data->osd->initialized) {
    printf("⚠️ [SSE回调] OSD对象已失效，停止处理数据\n");
    return 0;
}

// 重新分配缓冲区，添加错误检查
gchar *new_buffer = g_realloc(sse_data->buffer, sse_data->buffer_size + realsize + 1);
if (new_buffer == NULL) {
    g_warning("SSE缓冲区内存分配失败");
    return 0;
}
sse_data->buffer = new_buffer;
```

### 2. 增强线程安全性

```c
// 添加更多的有效性检查
while (osd && osd->initialized) {
    // 再次检查OSD对象有效性
    if (!osd || !osd->initialized) {
        printf("⚠️ [SSE线程] OSD对象已失效，退出连接循环\n");
        break;
    }
    
    // ... 连接逻辑
    
    // 使用更短的睡眠间隔，以便更快响应程序退出
    for (int i = 0; i < 30 && osd && osd->initialized; i++) {
        g_usleep(100 * 1000); // 100ms * 30 = 3秒
    }
}
```

### 3. 优化定时器清理

```c
// 改进的KRC状态清理
static void clear_krc_state(void) {
    printf("🔄 [KRC清理] 开始清理KRC状态\n");
    
    // 先标记为非活动状态，防止定时器回调继续执行
    krc_progress_state.is_active = FALSE;
    
    // 停止定时器
    if (krc_progress_state.timer_id > 0) {
        if (g_source_remove(krc_progress_state.timer_id)) {
            printf("🔄 [KRC清理] 已停止KRC渐进式播放定时器 (ID: %u)\n", krc_progress_state.timer_id);
        } else {
            printf("⚠️ [KRC清理] 定时器已不存在或已被移除 (ID: %u)\n", krc_progress_state.timer_id);
        }
        krc_progress_state.timer_id = 0;
    }
    
    // ... 其他清理逻辑
}
```

### 4. 添加信号处理器

```c
// 信号处理器
static void signal_handler(int sig) {
    printf("🛑 [信号] 收到信号 %d，开始优雅退出\n", sig);
    
    // 清理资源
    osd_lyrics_cleanup();
    
    // 退出GTK主循环
    if (gtk_main_level() > 0) {
        gtk_main_quit();
    }
    
    printf("👋 [信号] 程序退出\n");
    exit(0);
}

// 在main函数中注册信号处理器
signal(SIGINT, signal_handler);   // Ctrl+C
signal(SIGTERM, signal_handler);  // 终止信号
signal(SIGHUP, signal_handler);   // 挂起信号
```

### 5. 改进资源清理流程

```c
void osd_lyrics_cleanup(void) {
    printf("🧹 [清理] 开始清理OSD歌词资源\n");
    
    // 首先标记为未初始化，防止其他线程继续访问
    if (osd) {
        osd->initialized = FALSE;
    }
    
    // 清理KRC状态（包括定时器）
    clear_krc_state();
    
    // ... 详细的清理逻辑
    
    printf("✅ [清理] 所有资源清理完成\n");
}
```

## 测试验证

创建了稳定性测试脚本 `test_stability.sh`，包含以下测试：

1. **短时间运行测试** (10秒)
2. **中等时间运行测试** (30秒)  
3. **信号处理测试** (SIGTERM响应)
4. **内存泄漏检测** (如果有valgrind)

运行测试：
```bash
./test_stability.sh
```

## 修复效果

修复后的程序具有以下改进：

1. ✅ **更好的错误处理**: 增加了输入验证和错误检查
2. ✅ **增强的线程安全**: 改进了线程间的同步机制
3. ✅ **完善的资源清理**: 确保所有资源都被正确释放
4. ✅ **优雅的程序退出**: 支持信号处理和优雅关闭
5. ✅ **详细的调试日志**: 便于问题诊断和监控

## 建议

1. **定期测试**: 建议在不同环境下进行长时间运行测试
2. **监控日志**: 关注程序运行日志，及时发现潜在问题
3. **内存监控**: 定期使用valgrind等工具检查内存使用情况
4. **版本控制**: 保持代码版本控制，便于问题追踪

## 结论

通过以上修复，OSD歌词程序的稳定性得到了显著提升，应该能够解决播放几首歌曲后退出的问题。建议用户更新到修复版本并进行测试验证。
