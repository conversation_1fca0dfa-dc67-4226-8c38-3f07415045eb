// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

/**
 * MediaKeyService 媒体键服务
 * @module
 */

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unused imports
import { Call as $Call, CancellablePromise as $CancellablePromise, Create as $Create } from "@wailsio/runtime";

/**
 * EmitMPRISSeeked 发射MPRIS Seeked信号
 * @param {number} position
 * @returns {$CancellablePromise<void>}
 */
export function EmitMPRISSeeked(position) {
    return $Call.ByID(1943722875, position);
}

/**
 * GetMediaKeyStatus 获取媒体键状态
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function GetMediaKeyStatus() {
    return $Call.ByID(717811311).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * HandleMediaKeyEvent 处理媒体键事件（供前端调用）
 * @param {string} action
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function HandleMediaKeyEvent(action) {
    return $Call.ByID(2745699837, action).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * IsRegistered 检查是否已注册
 * @returns {$CancellablePromise<boolean>}
 */
export function IsRegistered() {
    return $Call.ByID(1906405598);
}

/**
 * NotifySeek 通知跳转事件（供前端调用）
 * @param {number} position
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function NotifySeek(position) {
    return $Call.ByID(690844715, position).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * RegisterMediaKeys 注册媒体键
 * @returns {$CancellablePromise<void>}
 */
export function RegisterMediaKeys() {
    return $Call.ByID(1820877813);
}

/**
 * SetApp 设置应用实例
 * @param {any} app
 * @returns {$CancellablePromise<void>}
 */
export function SetApp(app) {
    return $Call.ByID(1463270849, app);
}

/**
 * SetContext 设置上下文
 * @returns {$CancellablePromise<void>}
 */
export function SetContext() {
    return $Call.ByID(2357162989);
}

/**
 * SetPlayerService 设置播放器服务（预留接口）
 * @param {any} playerService
 * @returns {$CancellablePromise<void>}
 */
export function SetPlayerService(playerService) {
    return $Call.ByID(492185278, playerService);
}

/**
 * UnregisterMediaKeys 取消注册媒体键
 * @returns {$CancellablePromise<void>}
 */
export function UnregisterMediaKeys() {
    return $Call.ByID(1210209780);
}

/**
 * UpdateMPRISMetadata 更新MPRIS歌曲元数据
 * @param {string} title
 * @param {string} artist
 * @param {string} album
 * @param {string} artUrl
 * @param {number} duration
 * @returns {$CancellablePromise<void>}
 */
export function UpdateMPRISMetadata(title, artist, album, artUrl, duration) {
    return $Call.ByID(2362784993, title, artist, album, artUrl, duration);
}

/**
 * UpdateMPRISPlaybackStatus 更新MPRIS播放状态
 * @param {string} status
 * @returns {$CancellablePromise<void>}
 */
export function UpdateMPRISPlaybackStatus(status) {
    return $Call.ByID(1680353575, status);
}

/**
 * UpdateMPRISPosition 更新MPRIS播放位置
 * @param {number} position
 * @returns {$CancellablePromise<void>}
 */
export function UpdateMPRISPosition(position) {
    return $Call.ByID(3606360519, position);
}

/**
 * UpdateMPRISVolume 更新MPRIS音量
 * @param {number} volume
 * @returns {$CancellablePromise<void>}
 */
export function UpdateMPRISVolume(volume) {
    return $Call.ByID(503862662, volume);
}

/**
 * UpdatePlaybackStatus 更新播放状态（供前端调用）
 * @param {string} status
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function UpdatePlaybackStatus(status) {
    return $Call.ByID(3034457328, status).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * UpdatePlayerPosition 更新播放位置（供前端调用）
 * @param {number} position
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function UpdatePlayerPosition(position) {
    return $Call.ByID(2188869573, position).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * UpdatePlayerVolume 更新播放器音量（供前端调用）
 * @param {number} volume
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function UpdatePlayerVolume(volume) {
    return $Call.ByID(3618666188, volume).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

/**
 * UpdateSongMetadata 更新歌曲元数据（供前端调用）
 * @param {string} title
 * @param {string} artist
 * @param {string} album
 * @param {string} artUrl
 * @param {number} duration
 * @returns {$CancellablePromise<{ [_: string]: any }>}
 */
export function UpdateSongMetadata(title, artist, album, artUrl, duration) {
    return $Call.ByID(2552485143, title, artist, album, artUrl, duration).then(/** @type {($result: any) => any} */(($result) => {
        return $$createType0($result);
    }));
}

// Private type creation functions
const $$createType0 = $Create.Map($Create.Any, $Create.Any);
